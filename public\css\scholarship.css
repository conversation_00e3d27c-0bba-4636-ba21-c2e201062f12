* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

body {
    background-color: #d9d9d9;
    color: #333;
    line-height: 1.6;
    padding: 0;
    margin: 0;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Header and user actions styling */
.university-header {
    background-color: #fff;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    padding: 15px 0;
    margin-bottom: 30px;
}

.header-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.university-logo-title {
    display: flex;
    align-items: center;
}

.university-logo {
    height: 60px;
    margin-right: 15px;
}

.university-title h1 {
    font-size: 20px;
    margin: 0;
    color: #052F11;
}

.university-title h2 {
    font-size: 16px;
    margin: 5px 0 0;
    font-weight: normal;
    color: #555;
}

.user-actions {
    display: flex;
    align-items: center;
}

.logout-btn {
    display: inline-flex;
    align-items: center;
    background-color: #052F11;
    color: white;
    padding: 8px 15px;
    border-radius: 4px;
    text-decoration: none;
    font-size: 14px;
    transition: background-color 0.3s ease;
}

.logout-btn i {
    margin-right: 5px;
}

.logout-btn:hover {
    background-color: #052F11;
}

@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        text-align: center;
    }

    .university-logo-title {
        margin-bottom: 15px;
    }

    .user-actions {
        margin-top: 10px;
    }
}

/* Dashboard Banner Styling */
.dashboard-banner {
    background-color: #d9d9d9;
    padding: 20px 0;
    border: none;
    width: 100%;
}

.banner-container {
    max-width: 1200px;
    margin: 0 auto;
    text-align: center;
}

.dashboard-banner h2 {
    margin: 0;
    font-size: 1.2rem;
    font-weight: 600;
    color: #000;
    text-transform: uppercase;
    letter-spacing: 1px;
    display: inline-block;
}

/* Application container */
.application-container {
    max-width: 900px;
    margin: 20px auto 40px;
    padding: 2rem;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

/* Form header styling */
.form-header {
    position: relative;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #e0e0e0;
    display: flex;
    justify-content: space-between;
    align-items: flex-end;
}

.form-header h1 {
    color: #052F11;
    font-size: 28px;
    margin: 0;
    padding: 0;
}

.verification-text {
    color: #666;
    font-size: 14px;
    margin: 0;
    padding-bottom: 5px;
    font-style: italic;
}

/* Scholarship tabs styling */
.scholarship-tabs {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-bottom: 25px;
}

.tab-btn {
    padding: 10px 15px;
    background-color: #f5f5f5;
    border: 1px solid #ddd;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.3s ease;
}

.tab-btn.active {
    background-color: #052F11;
    color: white;
    border-color: #052F11;
}

.tab-btn:hover:not(.active) {
    background-color: #e9e9e9;
}

.tab-btn.add-more {
    background-color: white;
    border: 1px dashed #ccc;
    color: #666;
}

.tab-btn.add-more:hover {
    background-color: #f9f9f9;
    border-color: #aaa;
}

@media (max-width: 768px) {
    .form-header {
        flex-direction: column;
        align-items: flex-start;
    }

    .verification-text {
        margin-top: 5px;
        align-self: flex-end;
    }

    .scholarship-tabs {
        overflow-x: auto;
        padding-bottom: 5px;
    }
}

/* Form header styling */
.form-header h1 {
    color: #052F11;
    font-size: 1.5rem;
    font-weight: 600;
    margin: 0;
    line-height: 1.2;
    margin-bottom: 0.2rem;
    display: block;
    width: 100%;
}

.verification-text {
    color: #666;
    font-size: 0.85rem;
    font-weight: normal;
    margin: 0 0 1rem 0;
    line-height: 1.2;
    display: block;
    width: 100%;
}

.header-divider {
    border: none;
    height: 1px;
    background-color: #e0e0e0;
    margin: 0;
    clear: both;
}


/* Scholarship tabs */
.scholarship-tabs {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 1.5rem;
    gap: 5px;
}

.tab-btn {
    padding: 0.6rem 1.2rem;
    background-color: #f5f5f5;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 0.85rem;
    font-weight: 500;
    color: #444;
    cursor: pointer;
    transition: all 0.2s ease;
}

.tab-btn.active {
    background-color: #052F11;
    color: white;
    border-color: #052F11;
}

.tab-btn.add-more {
    background-color: white;
    border: 1px dashed #aaa;
    color: #666;
}

.tab-btn.add-more:hover {
    background-color: #e0f0e3;
    transform: translateY(-2px);
}

.form-section {
    margin-bottom: 1.5rem;
}

.form-row {
    display: flex;
    flex-wrap: wrap;
    gap: 1.2rem;
    margin-bottom: 1.5rem;
}

.form-group {
    flex: 1;
    min-width: 200px;
}

.address-street {
    flex: 2;
}

label {
    display: block;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
    font-weight: 500;
    color: #444;
}

input[type="text"],
input[type="email"],
input[type="tel"],
input[type="date"],
select {
    width: 100%;
    padding: 0.85rem;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    background-color: #f5f5f5;
    font-size: 0.95rem;
    transition: all 0.2s ease;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.05);
}

input[type="text"]:focus,
input[type="email"]:focus,
input[type="tel"]:focus,
input[type="date"]:focus,
select:focus {
    outline: none;
    border-color: #052F11;
    background-color: #fff;
    box-shadow: 0 0 0 3px rgba(5, 47, 17, 0.1);
}

input::placeholder {
    color: #aaa;
}

/* Radio button styling with green selection */
.radio-group {
    display: flex;
    gap: 2rem;
    margin-top: 0.5rem;
}

.radio-label {
    display: flex;
    align-items: center;
    cursor: pointer;
    position: relative;
    padding-left: 30px;
    user-select: none;
    margin-bottom: 0.5rem;
}

.radio-label input {
    position: absolute;
    opacity: 0;
    cursor: pointer;
}

.radio-text {
    font-size: 1rem;
    color: #555;
    margin-left: 5px;
    font-weight: normal;
}

/* Basic circle for radio button */
.radio-custom {
    position: absolute;
    left: 0;
    top: 0;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    border: 2px solid #ccc;
    background-color: #fff;
    transition: all 0.2s ease;
}

/* Selected state - green fill */
.radio-label input:checked + .radio-custom {
    background-color: #052F11;
    border-color: #fff;
    box-shadow: 0 0 0 2px #052F11;
}

/* Inner dot for selected state */
.radio-label input:checked + .radio-custom:after {
    content: '';
    position: absolute;
    top: 4px;
    left: 4px;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #1e5631;
}

/* Form section with icon styling */
.form-section h2 {
    display: flex;
    align-items: center;
    color: #1e5631;
    font-size: 1.4rem;
    margin-bottom: 1.5rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #e0e0e0;
}

.form-section h2 i {
    margin-right: 10px;
    background-color: #1e5631;
    color: white;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
}

hr {
    margin: 2.5rem 0;
    border: none;
    height: 1px;
    background: linear-gradient(to right, transparent, #e0e0e0, transparent);
}

.input-with-info {
    position: relative;
    display: flex;
    align-items: center;
}

.info-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background-color: #1e5631;
    color: white;
    font-size: 0.8rem;
    font-style: italic;
    margin-left: 0.5rem;
    cursor: help;
    transition: all 0.2s ease;
}

.info-icon:hover {
    background-color: #154a1c;
    transform: scale(1.1);
}

/* Improved dropdown styling */
select {
    appearance: none;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23666' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: right 12px center;
    background-size: 16px;
    padding-right: 40px;
    max-width: 400px; /* Limit width of dropdowns */
    width: 100%;
}

.form-submit {
    margin-top: 2.5rem;
    text-align: center;
}

.submit-btn {
    padding: 0.85rem 3.5rem;
    background-color: #052F11;
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    text-transform: uppercase;
    transition: all 0.3s ease;
    box-shadow: 0 4px 6px rgba(5, 47, 17, 0.2);
    letter-spacing: 0.5px;
}

.submit-btn:hover {
    background-color: #052F11;
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(5, 47, 17, 0.25);
}

.submit-btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(5, 47, 17, 0.2);
}

/* Error styling */
input.error, select.error {
    border-color: #e74c3c;
    background-color: #fff8f8;
    box-shadow: 0 0 0 3px rgba(231, 76, 60, 0.1);
}

.error-message {
    color: #e74c3c;
    font-size: 0.8rem;
    margin-top: 0.3rem;
    animation: fadeIn 0.3s ease;
}

/* Form transitions */
.scholarship-form {
    opacity: 0;
    height: 0;
    overflow: hidden;
    transition: opacity 0.3s ease, height 0s ease 0.3s;
}

.scholarship-form.active {
    opacity: 1;
    height: auto;
    overflow: visible;
    transition: opacity 0.3s ease;
}

/* Improved form section styling */
h2 {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1.3rem;
    margin: 1.8rem 0 1.2rem;
    color: #1e5631;
    font-weight: 500;
}

h2 i {
    font-size: 1.1rem;
    color: #1e5631;
}

hr {
    margin: 2.5rem 0;
    border: none;
    height: 1px;
    background: linear-gradient(to right, transparent, #e0e0e0, transparent);
}

/* Responsive improvements */
@media (max-width: 768px) {
    .application-container {
        padding: 1.5rem;
        margin: 1rem;
        border-radius: 8px;
    }

    .form-row {
        flex-direction: column;
        gap: 1rem;
    }

    .form-group {
        width: 100%;
    }

    .scholarship-tabs {
        flex-wrap: nowrap;
        overflow-x: auto;
        padding-bottom: 10px;
        margin-bottom: 1.5rem;
        -webkit-overflow-scrolling: touch;
        scrollbar-width: none; /* Firefox */
    }

    .scholarship-tabs::-webkit-scrollbar {
        display: none; /* Chrome, Safari, Edge */
    }

    .tab-btn {
        white-space: nowrap;
        flex: 0 0 auto;
        padding: 0.7rem 1rem;
        font-size: 0.85rem;
    }
}

.strand-field {
    flex: 1;
    min-width: 200px;
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Form tabs and switching */
.scholarship-form {
    display: none;
}

.scholarship-form.active {
    display: block;
    animation: fadeIn 0.4s ease-in-out;
}

.tab-btn {
    position: relative;
    overflow: hidden;
}

.tab-btn::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 3px;
    background-color: #052F11;
    transition: width 0.3s ease;
}

.tab-btn:hover::after {
    width: 100%;
}

.tab-btn.active::after {
    width: 100%;
}

.other-scholarship {
    transition: all 0.3s ease;
}

/* Student ID field specific styling */
#student_id,
#pd_student_id,
#emp_student_id,
#prv_student_id {
    max-width: 250px;
    width: 100%;
}

/* Adjust form group for student ID */
.form-group.student-id-group {
    flex: 0 0 auto;
    min-width: 200px;
    max-width: 250px;
}

/* Logo and header styling */
.form-header {
    display: flex;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1.5rem;
    border-bottom: 1px solid #e9ecef;
}

.logo-container {
    flex: 0 0 auto;
    margin-right: 1.5rem;
}

.form-logo {
    width: 100px;
    height: auto;
    object-fit: contain;
}

.header-text {
    flex: 1;
}

/* Responsive adjustments for the header */
@media (max-width: 576px) {
    .form-header {
        flex-direction: column;
        text-align: center;
    }

    .logo-container {
        margin-right: 0;
        margin-bottom: 1rem;
    }

    .form-logo {
        width: 80px;
    }
}

/* Responsive adjustments for the header */
@media (max-width: 768px) {
    .university-header {
        padding: 10px 15px;
    }

    .university-logo {
        width: 45px;
    }

    .university-title h1 {
        font-size: 1.2rem;
    }

    .university-title h2 {
        font-size: 0.8rem;
    }
}

@media (max-width: 576px) {
    .university-logo-title {
        gap: 10px;
    }

    .university-title h1 {
        font-size: 1rem;
    }

    .university-title h2 {
        font-size: 0.7rem;
    }
}

/* Form header styling to match image */
.form-header {
    margin-bottom: 1rem;
}

.form-header h1 {
    color: #052F11;
    font-size: 1.6rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.3rem;
}

.form-header p {
    color: #666;
    font-size: 0.9rem;
    margin-top: 0;
}

.form-verification-text {
    color: #666;
    font-size: 0.9rem;
    margin-top: -0.5rem;
    margin-bottom: 1rem;
    text-align: left; /* or center, depending on your layout */
}

/* Success page specific styles */
.success-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
}

.success-icon {
    text-align: center;
    margin-bottom: 20px;
    display: none; /* Hide the icon as it's not in the image */
}

.success-container h1 {
    font-size: 24px;
    margin-bottom: 10px;
    color: #333;
}

.success-container p {
    margin-bottom: 20px;
    line-height: 1.5;
}

.success-details {
    margin-bottom: 30px;
}

.detail-item {
    margin-bottom: 5px;
}

.detail-label {
    font-weight: bold;
    display: inline;
}

.detail-value {
    display: inline;
    margin-left: 5px;
}

.next-steps {
    margin-bottom: 30px;
}

.next-steps h2 {
    font-size: 18px;
    margin-bottom: 10px;
}

.next-steps ol {
    padding-left: 20px;
    margin-top: 5px;
}

.next-steps li {
    margin-bottom: 8px;
}

.action-buttons {
    margin-top: 20px;
}

.action-buttons a {
    display: inline-block;
    margin-right: 15px;
    text-decoration: none;
    color: #1e5631;
    font-weight: bold;
}

.action-buttons a:hover {
    text-decoration: underline;
}










