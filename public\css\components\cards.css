/* Card Component Styles */

/* Basic Cards */
.card {
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.card-header {
    background-color: #f8f9fa;
    padding: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #eee;
}

.card-header h3 {
    margin: 0;
    color: #333;
}

.card-body {
    padding: 30px;
}

/* Action Cards */
.action-card {
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    padding: 20px;
    text-align: center;
    transition: transform 0.3s ease;
    cursor: pointer;
    text-decoration: none;
    color: inherit;
}

.action-card:hover {
    transform: translateY(-5px);
    text-decoration: none;
    color: inherit;
}

.action-icon {
    font-size: 32px;
    color: #1e5631;
    margin-bottom: 10px;
}

.action-title {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 5px;
}

.action-description {
    font-size: 12px;
    color: #666;
}

/* Statistics Cards */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    padding: 20px;
    transition: transform 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
}

.stat-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.stat-title {
    margin: 0;
    font-size: 14px;
    color: #666;
    font-weight: 500;
}

.stat-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    color: white;
}

.stat-icon.total {
    background-color: #1e5631;
}

.stat-icon.pending {
    background-color: #ffc107;
}

.stat-icon.approved {
    background-color: #28a745;
}

.stat-icon.rejected {
    background-color: #dc3545;
}

.stat-value {
    font-size: 32px;
    font-weight: bold;
    color: #333;
    margin-bottom: 10px;
}

.stat-change {
    font-size: 12px;
    display: flex;
    align-items: center;
    gap: 5px;
}

.stat-change.positive {
    color: #28a745;
}

.stat-change.negative {
    color: #dc3545;
}

.stat-change.neutral {
    color: #6c757d;
}

/* Category Cards */
.category-card {
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    padding: 25px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.category-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}

.category-icon {
    font-size: 48px;
    color: #1e5631;
    margin-bottom: 15px;
}

.category-title {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 10px;
    color: #333;
}

.category-description {
    font-size: 14px;
    color: #666;
}

/* Section Cards */
.settings-section {
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.section-header {
    background-color: #1e5631;
    color: white;
    padding: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.section-header h3 {
    margin: 0;
    font-size: 18px;
}

.section-body {
    padding: 30px;
}

/* Table Containers */
.table-container,
.student-table-container {
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.table-header {
    background-color: #f8f9fa;
    padding: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #eee;
}

.table-header h3 {
    margin: 0;
    color: #333;
}

.table-actions {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

/* Enhanced Responsive Card Design */

/* Touch-friendly improvements */
@media (hover: none) and (pointer: coarse) {
    .action-card,
    .category-card,
    .stat-card {
        min-height: 120px;
        padding: 24px;
    }

    .action-icon,
    .category-icon {
        font-size: 36px;
    }

    .table-actions button,
    .table-actions .btn {
        min-height: 44px;
        padding: 12px 16px;
        touch-action: manipulation;
    }
}

/* Large tablets and small desktops */
@media (max-width: 1024px) {
    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
        gap: 15px;
    }

    .card-body,
    .section-body {
        padding: 25px;
    }

    .card-header,
    .table-header,
    .section-header {
        padding: 15px 20px;
    }
}

/* Tablets */
@media (max-width: 768px) {
    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 12px;
    }

    .card-body,
    .section-body {
        padding: 20px;
    }

    .card-header,
    .table-header,
    .section-header {
        padding: 15px;
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .table-actions {
        width: 100%;
        justify-content: flex-start;
    }

    .action-card,
    .category-card {
        padding: 18px;
    }

    .action-icon,
    .category-icon {
        font-size: 28px;
    }

    .action-title,
    .category-title {
        font-size: 15px;
    }

    .action-description,
    .category-description {
        font-size: 12px;
    }

    .stat-card {
        padding: 18px;
    }

    .stat-value {
        font-size: 28px;
    }

    .stat-icon {
        width: 36px;
        height: 36px;
        font-size: 16px;
    }
}

/* Mobile devices */
@media (max-width: 480px) {
    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 10px;
    }

    .card-body,
    .section-body {
        padding: 15px;
    }

    .card-header,
    .table-header,
    .section-header {
        padding: 12px;
    }

    .card-header h3,
    .table-header h3,
    .section-header h3 {
        font-size: 16px;
    }

    .table-actions {
        flex-direction: column;
        gap: 8px;
    }

    .table-actions button,
    .table-actions .btn {
        width: 100%;
        justify-content: center;
    }

    .action-card,
    .category-card {
        padding: 15px;
    }

    .action-icon,
    .category-icon {
        font-size: 24px;
    }

    .action-title,
    .category-title {
        font-size: 14px;
    }

    .action-description,
    .category-description {
        font-size: 11px;
    }

    .stat-card {
        padding: 15px;
    }

    .stat-value {
        font-size: 24px;
    }

    .stat-title {
        font-size: 13px;
    }

    .stat-icon {
        width: 32px;
        height: 32px;
        font-size: 14px;
    }

    .stat-change {
        font-size: 11px;
    }
}

/* Extra small devices */
@media (max-width: 320px) {
    .stats-grid {
        grid-template-columns: 1fr;
        gap: 8px;
    }

    .card-body,
    .section-body {
        padding: 12px;
    }

    .card-header,
    .table-header,
    .section-header {
        padding: 10px;
    }

    .card-header h3,
    .table-header h3,
    .section-header h3 {
        font-size: 15px;
    }

    .action-card,
    .category-card {
        padding: 12px;
    }

    .action-icon,
    .category-icon {
        font-size: 20px;
    }

    .action-title,
    .category-title {
        font-size: 13px;
    }

    .action-description,
    .category-description {
        font-size: 10px;
    }

    .stat-card {
        padding: 12px;
    }

    .stat-value {
        font-size: 20px;
    }

    .stat-title {
        font-size: 12px;
    }

    .stat-icon {
        width: 28px;
        height: 28px;
        font-size: 12px;
    }

    .stat-change {
        font-size: 10px;
    }
}
