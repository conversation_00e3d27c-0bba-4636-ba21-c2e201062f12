/* Enhanced Responsive Form Component Styles */

/* Form Groups */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #333;
    font-size: 14px;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 12px;
    border: 2px solid #ddd;
    border-radius: 8px;
    font-size: 14px;
    transition: all 0.3s ease;
    background-color: #fafafa;
    box-sizing: border-box;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #1e5631;
    background-color: white;
    box-shadow: 0 0 0 3px rgba(30, 86, 49, 0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 100px;
}

.form-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}

.form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #eee;
    flex-wrap: wrap;
}

/* Button Styles */
.form-actions .btn {
    padding: 12px 24px;
    border-radius: 8px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    border: none;
    font-size: 14px;
    min-width: 120px;
}

.form-actions .btn-primary {
    background: #052f11;
    color: white;
}

.form-actions .btn-primary:hover {
    background: #041f0c;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(5, 47, 17, 0.3);
}

.form-actions .btn-secondary {
    background-color: #6c757d;
    color: white;
}

.form-actions .btn-secondary:hover {
    background-color: #5a6268;
    transform: translateY(-1px);
}

/* Responsive Form Design */
@media (max-width: 768px) {
    .form-row {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .form-group {
        margin-bottom: 15px;
    }

    .form-group input,
    .form-group select,
    .form-group textarea {
        padding: 10px;
        font-size: 16px; /* Prevents zoom on iOS */
    }

    .form-actions {
        flex-direction: column;
        gap: 10px;
    }

    .form-actions .btn {
        width: 100%;
        justify-content: center;
        min-width: auto;
    }
}

@media (max-width: 480px) {
    .form-group label {
        font-size: 13px;
        margin-bottom: 6px;
    }

    .form-group input,
    .form-group select,
    .form-group textarea {
        padding: 8px;
        font-size: 16px;
    }

    .form-actions {
        margin-top: 20px;
        padding-top: 15px;
    }

    .form-actions .btn {
        padding: 10px 20px;
        font-size: 13px;
    }
}

@media (max-width: 320px) {
    .form-group {
        margin-bottom: 12px;
    }

    .form-group label {
        font-size: 12px;
        margin-bottom: 5px;
    }

    .form-group input,
    .form-group select,
    .form-group textarea {
        padding: 6px;
        font-size: 14px;
    }

    .form-actions .btn {
        padding: 8px 16px;
        font-size: 12px;
    }
}

/* Buttons */
.btn-primary,
.btn-secondary,
.btn-danger {
    padding: 10px 20px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 14px;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    margin-right: 10px;
    transition: all 0.2s ease;
}

.btn-primary {
    background-color: #1e5631;
    color: white;
}

.btn-primary:hover {
    background-color: #164023;
}

.btn-secondary {
    background-color: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background-color: #5a6268;
}

.btn-danger {
    background-color: #dc3545;
    color: white;
}

.btn-danger:hover {
    background-color: #c82333;
}

/* Action Buttons */
.action-btn {
    padding: 5px 8px;
    margin: 0 2px;
    border: none;
    border-radius: 3px;
    cursor: pointer;
    font-size: 12px;
    background-color: #1e5631;
    color: white;
    transition: all 0.2s ease;
}

.action-btn:hover {
    background-color: #164023;
}

.action-btn.delete {
    background-color: #dc3545;
}

.action-btn.delete:hover {
    background-color: #c82333;
}

/* Toggle Switches - Using modal-specific styles instead */

/* Status Badges */
.status-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.status-badge.active {
    background-color: #d4edda;
    color: #155724;
}

.status-badge.pending {
    background-color: #fff3cd;
    color: #856404;
}

.status-badge.approved {
    background-color: #d4edda;
    color: #155724;
}

.status-badge.rejected {
    background-color: #f8d7da;
    color: #721c24;
}




