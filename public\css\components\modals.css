/* Modal Styles */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: flex-start;
    z-index: 1000;
    animation: fadeIn 0.3s ease;
    overflow-y: auto;
    padding: 20px 0;
}

.modal-content {
    background: white;
    border-radius: 12px;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
    max-width: 600px;
    width: 90%;
    max-height: none;
    margin: auto;
    animation: slideIn 0.3s ease;
    display: flex;
    flex-direction: column;
}

.modal-header {
    padding: 20px;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    margin: 0;
    color: #333;
    font-size: 20px;
}

.close-btn {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #666;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.close-btn:hover {
    background-color: #f5f5f5;
    color: #333;
}

.modal-body {
    padding: 20px;
}

.modal-footer {
    padding: 20px;
    border-top: 1px solid #eee;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

/* Form Styles */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: #333;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 14px;
    transition: border-color 0.3s ease;
    font-family: inherit;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #1e5631;
    box-shadow: 0 0 0 2px rgba(30, 86, 49, 0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 100px;
}

.form-group small {
    display: block;
    margin-top: 5px;
    color: #666;
    font-size: 12px;
}

.form-group small a {
    color: #1e5631;
    text-decoration: none;
}

.form-group small a:hover {
    text-decoration: underline;
}

/* Button Styles */
.btn-primary {
    background-color: #1e5631;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 5px;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.3s ease;
}

.btn-primary:hover {
    background-color: #164023;
}

.btn-secondary {
    background-color: #6c757d;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 5px;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.3s ease;
}

.btn-secondary:hover {
    background-color: #5a6268;
}

/* Export Options */
.export-options {
    display: grid;
    grid-template-columns: 1fr;
    gap: 15px;
}

.export-option {
    border: 2px solid #eee;
    border-radius: 8px;
    padding: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
}

.export-option:hover {
    border-color: #1e5631;
    background-color: #f8f9fa;
}

.export-option i {
    font-size: 32px;
    color: #1e5631;
    margin-bottom: 10px;
}

.export-option h4 {
    margin: 10px 0 5px;
    color: #333;
    font-size: 16px;
}

.export-option p {
    margin: 0;
    color: #666;
    font-size: 14px;
}

/* Settings Sections */
.settings-section {
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 1px solid #eee;
}

.settings-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.settings-section h4 {
    margin: 0 0 15px;
    color: #333;
    font-size: 16px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
}

.settings-section h4::before {
    content: '';
    width: 4px;
    height: 24px;
    background: #1e5631;
    border-radius: 2px;
}

/* Application Control Section */
.application-control-section {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 24px;
    background: #f8fffe;
    border-radius: 16px;
    border: 2px solid #e8f5e8;
    margin-bottom: 0;
    box-shadow: 0 4px 16px rgba(30, 86, 49, 0.08);
    position: relative;
}

.application-control-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: #1e5631;
}

.application-control-info {
    flex: 1;
    padding-right: 24px;
}

.application-control-info h5 {
    margin: 0 0 8px 0;
    font-weight: 700;
    color: #1e5631;
    font-size: 17px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.application-control-info h5::before {
    content: '🎯';
    font-size: 16px;
}

.application-control-info p {
    margin: 0;
    color: #5a6c57;
    font-size: 14px;
    line-height: 1.6;
    font-weight: 500;
}

.toggle-control {
    display: flex;
    align-items: center;
    flex-shrink: 0;
    padding: 12px 16px;
    background: #ffffff;
    border-radius: 12px;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
    border: 2px solid rgba(30, 86, 49, 0.1);
    gap: 12px;
}

.toggle-container {
    display: flex;
    align-items: center;
    gap: 12px;
}

/* Enhanced Toggle Switch Styles - High Specificity */
.modal-content .toggle-switch {
    position: relative;
    display: inline-block;
    width: 64px;
    height: 32px;
    flex-shrink: 0;
}

.modal-content .toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
    position: absolute;
}

.modal-content .toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: #e74c3c !important;
    border-radius: 32px;
    box-shadow: 0 2px 6px rgba(0,0,0,0.15);
    transition: all 0.3s ease;
}

.modal-content .toggle-slider:before {
    position: absolute;
    content: "";
    height: 26px;
    width: 26px;
    left: 3px;
    top: 3px;
    background: #ffffff;
    border-radius: 50%;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
    transition: transform 0.3s ease;
}

.modal-content .toggle-switch input:checked + .toggle-slider {
    background: #052f11 !important;
}

.modal-content .toggle-switch input:checked + .toggle-slider:before {
    transform: translateX(32px);
}

/* Alternative toggle switch styles for better compatibility */
.application-control-section .toggle-switch {
    position: relative;
    display: inline-block;
    width: 64px;
    height: 32px;
    flex-shrink: 0;
}

.application-control-section .toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
    position: absolute;
}

.application-control-section .toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: #e74c3c !important;
    border-radius: 32px;
    box-shadow: 0 2px 6px rgba(0,0,0,0.15);
    transition: all 0.3s ease;
}

.application-control-section .toggle-slider:before {
    position: absolute;
    content: "";
    height: 26px;
    width: 26px;
    left: 3px;
    top: 3px;
    background: #ffffff;
    border-radius: 50%;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
    transition: transform 0.3s ease;
}

.application-control-section .toggle-switch input:checked + .toggle-slider {
    background: #052f11 !important;
}

.application-control-section .toggle-switch input:checked + .toggle-slider:before {
    transform: translateX(32px);
}

/* Ultra-specific toggle switch for modal application toggle */
#modalApplicationToggle + .toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: #e74c3c !important;
    border-radius: 32px;
    box-shadow: 0 2px 6px rgba(0,0,0,0.15);
    transition: all 0.3s ease;
}

#modalApplicationToggle + .toggle-slider:before {
    position: absolute;
    content: "";
    height: 26px;
    width: 26px;
    left: 3px;
    top: 3px;
    background: #ffffff;
    border-radius: 50%;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
    transition: transform 0.3s ease;
}

#modalApplicationToggle:checked + .toggle-slider {
    background: #052f11 !important;
}

#modalApplicationToggle:checked + .toggle-slider:before {
    transform: translateX(32px);
}

.status-text {
    font-weight: 700;
    font-size: 14px;
    min-width: 60px;
    text-align: center;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes slideIn {
    from {
        transform: translateY(-50px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

/* Enhanced Responsive Design */
.modal-overlay {
    backdrop-filter: blur(2px);
    -webkit-backdrop-filter: blur(2px);
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
}

.modal-content {
    position: relative;
    overflow: hidden;
}

/* Touch-friendly improvements */
@media (hover: none) and (pointer: coarse) {
    .close-btn {
        width: 44px;
        height: 44px;
        font-size: 20px;
    }

    .btn-primary,
    .btn-secondary {
        min-height: 44px;
        padding: 12px 24px;
        touch-action: manipulation;
    }

    .export-option {
        padding: 24px;
        min-height: 120px;
    }
}

/* Large tablets and small desktops */
@media (max-width: 1024px) {
    .modal-content {
        max-width: 700px;
        width: 92%;
    }
}

/* Tablets */
@media (max-width: 768px) {
    .modal-content {
        width: 95%;
        max-height: 90vh;
        margin: 20px;
    }

    .modal-header,
    .modal-body,
    .modal-footer {
        padding: 15px;
    }

    .modal-header h3 {
        font-size: 18px;
    }

    .close-btn {
        width: 36px;
        height: 36px;
        font-size: 20px;
    }

    .export-options {
        grid-template-columns: 1fr;
        gap: 12px;
    }

    .export-option {
        padding: 18px;
    }

    .export-option i {
        font-size: 28px;
    }

    .export-option h4 {
        font-size: 15px;
    }

    .export-option p {
        font-size: 13px;
    }

    .modal-footer {
        flex-direction: column;
        gap: 8px;
    }

    .modal-footer button {
        width: 100%;
        margin-bottom: 0;
        justify-content: center;
    }

    .form-group input,
    .form-group select {
        font-size: 16px; /* Prevents zoom on iOS */
    }
}

/* Mobile devices */
@media (max-width: 480px) {
    .modal-content {
        width: 98%;
        max-height: 95vh;
        margin: 10px;
        border-radius: 8px;
    }

    .modal-header,
    .modal-body,
    .modal-footer {
        padding: 12px;
    }

    .modal-header h3 {
        font-size: 16px;
    }

    .close-btn {
        width: 32px;
        height: 32px;
        font-size: 18px;
    }

    .export-option {
        padding: 15px;
    }

    .export-option i {
        font-size: 24px;
    }

    .export-option h4 {
        font-size: 14px;
    }

    .export-option p {
        font-size: 12px;
    }

    .btn-primary,
    .btn-secondary {
        padding: 10px 16px;
        font-size: 13px;
    }

    .form-group {
        margin-bottom: 15px;
    }

    .form-group input,
    .form-group select {
        padding: 8px;
    }

    .column-list {
        gap: 6px;
    }

    .required-column,
    .optional-column {
        padding: 4px 8px;
        font-size: 12px;
    }
}

/* Extra small devices */
@media (max-width: 320px) {
    .modal-content {
        width: 100%;
        max-height: 100vh;
        margin: 0;
        border-radius: 0;
    }

    .modal-header,
    .modal-body,
    .modal-footer {
        padding: 10px;
    }

    .modal-header h3 {
        font-size: 15px;
    }

    .close-btn {
        width: 30px;
        height: 30px;
        font-size: 16px;
    }

    .export-option {
        padding: 12px;
    }

    .export-option i {
        font-size: 20px;
    }

    .export-option h4 {
        font-size: 13px;
    }

    .export-option p {
        font-size: 11px;
    }

    .btn-primary,
    .btn-secondary {
        padding: 8px 12px;
        font-size: 12px;
    }

    .form-group input,
    .form-group select {
        padding: 6px;
        font-size: 14px;
    }

    .import-instructions {
        padding: 16px;
    }

    .instruction-header h4 {
        font-size: 16px;
    }

    .instruction-content p {
        font-size: 14px;
    }
}

/* File Input Styling */
input[type="file"] {
    padding: 8px !important;
    border: 2px dashed #ddd !important;
    background-color: #f9f9f9;
}

input[type="file"]:hover {
    border-color: #1e5631 !important;
    background-color: #f0f8f0;
}

/* Loading States */
.btn-primary:disabled,
.btn-secondary:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.loading {
    position: relative;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #fff;
    border-top: 2px solid transparent;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Import Modal Specific Styles */
.import-instructions {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 12px;
    padding: 24px;
    margin-top: 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.instruction-header {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 20px;
    padding-bottom: 16px;
    border-bottom: 2px solid #e9ecef;
}

.instruction-header i {
    color: #0066cc;
    font-size: 20px;
}

.instruction-header h4 {
    margin: 0;
    color: #2c3e50;
    font-size: 18px;
    font-weight: 600;
}

.instruction-content p {
    margin: 0 0 16px 0;
    color: #495057;
    font-size: 15px;
    font-weight: 500;
}

.column-list {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 20px;
}

.required-column,
.optional-column {
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 13px;
    font-weight: 500;
    white-space: nowrap;
}

.required-column {
    background-color: #e3f2fd;
    color: #1565c0;
    border: 1px solid #bbdefb;
}

.optional-column {
    background-color: #f3e5f5;
    color: #7b1fa2;
    border: 1px solid #e1bee7;
}

.import-notes {
    margin-bottom: 20px;
}

.note-item {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 10px;
    padding: 8px 0;
}

.note-item i {
    color: #28a745;
    font-size: 12px;
    width: 16px;
    text-align: center;
}

.note-item span {
    color: #495057;
    font-size: 14px;
}

.template-download {
    text-align: center;
    padding-top: 16px;
    border-top: 1px solid #e9ecef;
}

.download-link {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 10px 20px;
    background-color: #28a745;
    color: white;
    text-decoration: none;
    border-radius: 6px;
    font-weight: 500;
    transition: background-color 0.2s ease;
}

.download-link:hover {
    background-color: #218838;
    color: white;
    text-decoration: none;
}

.download-link i {
    font-size: 14px;
}

.form-group label input[type="checkbox"] {
    margin-right: 8px;
    transform: scale(1.1);
}

.form-section h3 {
    color: #1e5631;
    margin-bottom: 15px;
    font-size: 16px;
    font-weight: 600;
    border-bottom: 2px solid #e9ecef;
    padding-bottom: 8px;
}

/* File input styling for import */
input[type="file"] {
    padding: 12px !important;
    border: 2px dashed #1e5631 !important;
    background-color: #f8f9fa;
    border-radius: 8px !important;
    cursor: pointer;
    transition: all 0.3s ease;
}

input[type="file"]:hover {
    background-color: #e8f5e8;
    border-color: #164023 !important;
}

input[type="file"]:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(30, 86, 49, 0.1);
}

/* Progress indicator for import */
.import-progress {
    display: none;
    margin-top: 15px;
    padding: 10px;
    background-color: #e3f2fd;
    border-radius: 5px;
    border-left: 4px solid #2196f3;
}

.import-progress.show {
    display: block;
}

.progress-text {
    color: #1976d2;
    font-weight: 500;
    margin-bottom: 5px;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background-color: #e0e0e0;
    border-radius: 4px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background-color: #2196f3;
    transition: width 0.3s ease;
    width: 0%;
}

/* Import info styling */
.import-info {
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 5px;
    padding: 15px;
    margin-top: 15px;
}

.import-info p {
    margin: 0 0 10px 0;
    color: #495057;
    font-weight: 500;
}

.import-info ul {
    margin: 0;
    padding-left: 20px;
}

.import-info li {
    margin-bottom: 5px;
    color: #6c757d;
    font-size: 14px;
}

.import-info a {
    color: #1e5631;
    text-decoration: none;
    font-weight: 500;
}

.import-info a:hover {
    text-decoration: underline;
}

/* Selected file display */
#selectedFileName {
    color: #1e5631;
    font-weight: 600;
}

/* Hidden file input */
#hiddenFileInput {
    position: absolute;
    left: -9999px;
    opacity: 0;
    pointer-events: none;
}

/* Confirm Dialog Styles */
.confirm-dialog {
    max-width: 450px;
    text-align: center;
}

.confirm-icon {
    font-size: 48px;
    margin-bottom: 20px;
    color: #ffc107;
}

.confirm-message {
    font-size: 16px;
    color: #333;
    margin-bottom: 30px;
    line-height: 1.5;
}

.confirm-buttons {
    display: flex;
    gap: 15px;
    justify-content: center;
}

.confirm-buttons button {
    min-width: 100px;
    padding: 12px 24px;
    font-weight: 600;
}

/* Legacy Modal Support (for announcements and other pages) */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(2px);
    -webkit-backdrop-filter: blur(2px);
}

.modal.show {
    display: flex;
    justify-content: center;
    align-items: center;
}

/* Confirmation Modal Specific */
.confirmation-modal {
    max-width: 400px;
}

.confirmation-content {
    text-align: center;
    padding: 20px;
}

.warning-icon {
    font-size: 48px;
    color: #ffc107;
    margin-bottom: 15px;
    display: block;
}

.confirmation-content p {
    font-size: 16px;
    color: #333;
    margin: 0;
    line-height: 1.5;
}

/* Button variants */
.btn-danger {
    background-color: #dc3545;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 5px;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.3s ease;
}

.btn-danger:hover {
    background-color: #c82333;
}

/* Loading States for Modals */
.loading {
    text-align: center;
    padding: 40px 20px;
    color: #666;
}

.loading i {
    font-size: 24px;
    margin-bottom: 10px;
    color: #1e5631;
}

.loading p {
    margin: 0;
    font-size: 14px;
}

/* Close button for legacy modals */
.close {
    color: #aaa;
    float: right;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    line-height: 1;
    padding: 0;
    background: none;
    border: none;
}

.close:hover,
.close:focus {
    color: #333;
    text-decoration: none;
}

/* Modal header for legacy modals */
.modal-header h2 {
    margin: 0;
    font-size: 20px;
    color: #333;
}

/* Responsive adjustments for legacy modals */
@media (max-width: 768px) {
    .modal-content {
        width: 95%;
        margin: 5% auto;
        max-height: 90vh;
        overflow-y: auto;
    }

    .modal-header,
    .modal-body,
    .modal-footer {
        padding: 15px;
    }

    .close {
        font-size: 24px;
    }
}
