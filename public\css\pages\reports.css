/* Reports Page Specific Styles */

/* Report Categories */
.report-categories {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.category-card {
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    padding: 25px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.category-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}

.category-icon {
    font-size: 48px;
    color: #1e5631;
    margin-bottom: 15px;
}

.category-title {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 10px;
    color: #333;
}

.category-description {
    font-size: 14px;
    color: #666;
}

/* Report Panel Styles */
.report-panel,
.archive-panel {
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    margin-bottom: 30px;
    display: none;
}

.report-panel.active,
.archive-panel.active {
    display: block;
}

.panel-header {
    background-color: #1e5631;
    color: white;
    padding: 20px;
    border-radius: 10px 10px 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.panel-header h3 {
    margin: 0;
    font-size: 20px;
}

.close-panel-btn {
    background: none;
    border: none;
    color: white;
    font-size: 20px;
    cursor: pointer;
    padding: 5px;
    border-radius: 3px;
    transition: background-color 0.3s ease;
}

.close-panel-btn:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

.panel-body {
    padding: 30px;
}

/* Form Elements */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: #333;
}

.form-group select,
.form-group input {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 14px;
}

.form-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}

.custom-date-range {
    margin-top: 15px;
    display: none;
}

.custom-date-range.active {
    display: block;
}

/* Buttons */
.generate-btn,
.download-btn {
    background-color: #1e5631;
    color: white;
    border: none;
    padding: 12px 20px;
    border-radius: 5px;
    cursor: pointer;
    font-size: 14px;
    margin-right: 10px;
    transition: background-color 0.2s ease;
}

.generate-btn:hover,
.download-btn:hover {
    background-color: #164023;
}

.download-btn {
    background-color: #17a2b8;
}

.download-btn:hover {
    background-color: #138496;
}

/* Archive Table Styles */
.archive-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 20px;
}

.archive-table th,
.archive-table td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #eee;
}

.archive-table th {
    background-color: #f8f9fa;
    font-weight: 600;
    color: #333;
}

.archive-table tr:hover {
    background-color: #f8f9fa;
}

/* Action Buttons */
.action-btn {
    padding: 5px 8px;
    margin: 0 2px;
    border: none;
    border-radius: 3px;
    cursor: pointer;
    font-size: 12px;
    background-color: #1e5631;
    color: white;
    transition: all 0.2s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 4px;
}

.action-btn:hover {
    background-color: #164023;
    color: white;
    text-decoration: none;
}

.action-btn.download {
    background-color: #17a2b8;
}

.action-btn.download:hover {
    background-color: #138496;
}

.action-btn.delete {
    background-color: #dc3545;
}

.action-btn.delete:hover {
    background-color: #c82333;
}

/* Statistics Overview */
.stats-overview {
    margin-bottom: 30px;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.stat-card {
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    padding: 20px;
    transition: transform 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
}

.stat-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.stat-title {
    margin: 0;
    font-size: 14px;
    color: #666;
    font-weight: 500;
}

.stat-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    color: white;
}

.stat-icon.total {
    background-color: #1e5631;
}

.stat-icon.pending {
    background-color: #ffc107;
}

.stat-icon.approved {
    background-color: #28a745;
}

.stat-icon.rejected {
    background-color: #dc3545;
}

.stat-value {
    font-size: 32px;
    font-weight: bold;
    color: #333;
    margin-bottom: 10px;
}

.stat-change {
    font-size: 12px;
    display: flex;
    align-items: center;
    gap: 5px;
}

.stat-change.positive {
    color: #28a745;
}

.stat-change.negative {
    color: #dc3545;
}

.stat-change.neutral {
    color: #6c757d;
}

/* Loading States */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #1e5631;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: 40px 20px;
    color: #666;
}

.empty-state i {
    font-size: 48px;
    color: #ccc;
    margin-bottom: 15px;
}

.empty-state h3 {
    margin: 0 0 10px;
    color: #999;
}

.empty-state p {
    margin: 0;
    font-size: 14px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .report-categories {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .category-card {
        padding: 20px;
    }

    .category-icon {
        font-size: 36px;
    }

    .category-title {
        font-size: 16px;
    }

    .panel-body {
        padding: 20px;
    }

    .form-row {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .generate-btn,
    .download-btn {
        width: 100%;
        margin-bottom: 10px;
        margin-right: 0;
    }

    .archive-table {
        font-size: 12px;
    }

    .archive-table th,
    .archive-table td {
        padding: 8px;
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 15px;
    }
}

@media (max-width: 480px) {
    .stats-grid {
        grid-template-columns: 1fr;
    }

    .panel-header {
        padding: 15px;
    }

    .panel-header h3 {
        font-size: 18px;
    }

    .panel-body {
        padding: 15px;
    }
}

/* Additional styles for enhanced preview modal */
.stat-item.full-width {
    flex-direction: column !important;
    align-items: flex-start !important;
    padding: 12px 0;
    border-bottom: 1px solid #e9ecef;
}

.type-stat {
    display: inline-block;
    margin-right: 15px;
    font-size: 0.9rem;
    color: #495057;
    background: #e9ecef;
    padding: 2px 8px;
    border-radius: 4px;
    margin-bottom: 4px;
}

/* Enhanced button states */
.btn-primary:disabled,
.btn-secondary:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
}

/* Loading spinner for buttons */
.fa-spinner {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Report Preview Modal Styles */
.report-preview-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(2px);
}

.modal-content {
    position: relative;
    background: white;
    border-radius: 12px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
    max-width: 90vw;
    max-height: 90vh;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.modal-content.large-modal {
    width: 1000px;
    max-width: 95vw;
}

.modal-header {
    background-color: #1e5631;
    color: white;
    padding: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-radius: 12px 12px 0 0;
}

.modal-header h3 {
    margin: 0;
    font-size: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.close-btn {
    background: none;
    border: none;
    color: white;
    font-size: 20px;
    cursor: pointer;
    padding: 8px;
    border-radius: 4px;
    transition: background-color 0.3s ease;
}

.close-btn:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

.modal-body {
    padding: 0;
    flex: 1;
    overflow-y: auto;
    max-height: calc(90vh - 140px);
}

.preview-content {
    padding: 30px;
}

.preview-info {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 25px;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.info-item {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.info-label {
    font-weight: 600;
    color: #495057;
    font-size: 14px;
}

.info-value {
    color: #1e5631;
    font-weight: 500;
}

.preview-summary h4 {
    color: #1e5631;
    margin-bottom: 15px;
    font-size: 18px;
    border-bottom: 2px solid #e9ecef;
    padding-bottom: 8px;
}

.summary-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 25px;
}

.stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 15px;
    background-color: #f8f9fa;
    border-radius: 6px;
    border-left: 4px solid #1e5631;
}

.stat-label {
    font-weight: 500;
    color: #495057;
}

.stat-value {
    font-weight: 600;
    color: #1e5631;
    font-size: 16px;
}

.preview-data h4 {
    color: #1e5631;
    margin-bottom: 15px;
    font-size: 18px;
    border-bottom: 2px solid #e9ecef;
    padding-bottom: 8px;
}

.table-container {
    overflow-x: auto;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.preview-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 14px;
}

.preview-table th {
    background-color: #1e5631;
    color: white;
    padding: 12px 8px;
    text-align: left;
    font-weight: 600;
    white-space: nowrap;
}

.preview-table td {
    padding: 10px 8px;
    border-bottom: 1px solid #e9ecef;
    vertical-align: top;
}

.preview-table tr:nth-child(even) {
    background-color: #f8f9fa;
}

.preview-table tr:hover {
    background-color: #e3f2fd;
}

.status-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    text-transform: uppercase;
    white-space: nowrap;
}

.status-pending-review {
    background-color: #fff3cd;
    color: #856404;
}

.status-approved {
    background-color: #d4edda;
    color: #155724;
}

.status-rejected {
    background-color: #f8d7da;
    color: #721c24;
}

.status-under-committee-review {
    background-color: #d1ecf1;
    color: #0c5460;
}

.no-data {
    text-align: center;
    color: #6c757d;
    font-style: italic;
    padding: 40px;
    background-color: #f8f9fa;
    border-radius: 8px;
}

.modal-footer {
    padding: 20px;
    background-color: #f8f9fa;
    border-top: 1px solid #e9ecef;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

.btn-primary, .btn-secondary {
    padding: 10px 20px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
}

.btn-primary {
    background-color: #1e5631;
    color: white;
}

.btn-primary:hover {
    background-color: #164023;
    transform: translateY(-1px);
}

.btn-secondary {
    background-color: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background-color: #545b62;
    transform: translateY(-1px);
}

/* Responsive modal */
@media (max-width: 768px) {
    .modal-content.large-modal {
        width: 95vw;
        max-height: 95vh;
    }

    .preview-content {
        padding: 20px;
    }

    .preview-info {
        grid-template-columns: 1fr;
        gap: 10px;
    }

    .summary-stats {
        grid-template-columns: 1fr;
        gap: 10px;
    }

    .preview-table {
        font-size: 12px;
    }

    .preview-table th,
    .preview-table td {
        padding: 8px 6px;
    }

    .modal-footer {
        flex-direction: column;
    }

    .btn-primary, .btn-secondary {
        width: 100%;
        justify-content: center;
    }
}

/* Simple Notification Styles */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: white;
    border-radius: 4px;
    padding: 10px 14px;
    max-width: 300px;
    z-index: 1100;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 13px;
    border: 1px solid;
}

.notification-success {
    background-color: #f0f9f0;
    border-color: #28a745;
    color: #155724;
}

.notification-error {
    background-color: #fdf2f2;
    border-color: #dc3545;
    color: #721c24;
}

.notification-info {
    background-color: #f0f8ff;
    border-color: #17a2b8;
    color: #0c5460;
}

.notification-info .notification-content i {
    color: #17a2b8;
}

.notification-close {
    background: none;
    border: none;
    color: #6c757d;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: background-color 0.2s ease;
}

.notification-close:hover {
    background-color: #f8f9fa;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}
