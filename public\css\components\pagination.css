/* Pagination Component Styles */
.pagination-container {
    margin-top: 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.pagination-info {
    color: #666;
    font-size: 14px;
}

.pagination-wrapper {
    display: flex;
    justify-content: center;
}

.pagination {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
    align-items: center;
    gap: 5px;
}

.pagination li {
    margin: 0;
}

.pagination li a,
.pagination li span {
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 40px;
    height: 40px;
    padding: 0 12px;
    border-radius: 8px;
    text-decoration: none;
    color: #1e5631;
    background-color: white;
    border: 1px solid #e0e0e0;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.2s ease;
}

.pagination li.active span {
    background-color: #1e5631;
    color: white;
    border-color: #1e5631;
    box-shadow: 0 2px 4px rgba(30, 86, 49, 0.2);
}

.pagination li a:hover {
    background-color: #f8f9fa;
    border-color: #1e5631;
    color: #1e5631;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.pagination li.disabled span {
    color: #ccc;
    background-color: #f8f9fa;
    border-color: #e0e0e0;
    cursor: not-allowed;
}

.pagination li:first-child a,
.pagination li:last-child a {
    padding: 0 16px;
}

.pagination .page-link {
    border: 1px solid #e0e0e0;
}

.pagination-wrapper .hidden {
    display: none;
}

.pagination li span:contains("...") {
    border: none;
    background: none;
    color: #999;
    cursor: default;
}

.pagination i {
    font-size: 12px;
}

.pagination .disabled span {
    opacity: 0.5;
}

.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Mobile responsive */
@media (max-width: 768px) {
    .pagination-container {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }
    
    .pagination li a,
    .pagination li span {
        min-width: 35px;
        height: 35px;
        font-size: 13px;
    }
    
    .pagination-info {
        font-size: 12px;
    }
}
